import { json } from '@sveltejs/kit'
import type { Request<PERSON><PERSON><PERSON> } from './$types'
import { prisma } from '$lib/db'
import { log } from '$lib/logger'
import { hasAdvancedAccess } from '$lib/invite-codes'
import { getSession } from '$lib/session'

export const GET: RequestHandler = async ({ params, cookies }) => {
  try {
    const session = await getSession(cookies)

    if (!session?.user?.email) {
      return json({ error: 'Unauthorized' }, { status: 401 })
    }

    const taskId = params.id

    // 获取任务详情
    const task = await prisma.task.findUnique({
      where: {
        id: taskId
      },
      include: {
        level: {
          include: {
            progress: {
              where: {
                user: {
                  email: session.user.email
                }
              }
            },
            parent: {
              select: {
                id: true,
                name: true
              }
            }
          }
        }
      }
    })

    if (!task) {
      return json({ error: 'Task not found' }, { status: 404 })
    }

    // 获取用户信息以检查权限
    const user = await prisma.user.findUnique({
      where: { email: session.user.email! },
      select: { userType: true, score: true }
    })

    // 检查是否是受限关卡
    const parentName = task.level.parent?.name || task.level.name
    const hasAccess = hasAdvancedAccess(user?.userType || 'normal', user?.score || 0, parentName)

    if (!hasAccess) {
      const errorMessage = user?.userType === 'normal'
        ? (parentName === '进阶操作'
          ? '您需要达到500经验值才能访问此任务'
          : '您需要达到600经验值才能访问此任务')
        : (user?.userType === 'friend' && parentName === '实用技巧'
          ? '您需要达到600经验值才能访问此任务'
          : '您没有权限访问此任务，请使用邀请码注册以获得访问权限')

      return json(
        { error: errorMessage },
        { status: 403 }
      )
    }

    return json({
      task,
      level: task.level
    })
  } catch (error) {
    log.error('Error fetching task:', error)
    return json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
